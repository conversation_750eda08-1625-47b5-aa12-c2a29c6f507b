; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32cam]
platform = https://github.com/pioarduino/platform-espressif32/releases/download/54.03.20/platform-espressif32.zip
board = 4d_systems_esp32s3_gen4_r8n16
framework = arduino
monitor_speed = 115200
monitor_filters = esp32_exception_decoder
build_flags = 
	-DCORE_DEBUG_LEVEL=0
	-DBOARD_HAS_PSRAM
	-mfix-esp32-psram-cache-issue
lib_deps = 
	bblanchon/ArduinoJson@^7.0.0
	earlephilhower/BackgroundAudio@^1.3.3
	; esphome/ESP32-audioI2S@^2.3.0
upload_speed = 921600
board_build.partitions = huge_app.csv
board_build.filesystem = littlefs
