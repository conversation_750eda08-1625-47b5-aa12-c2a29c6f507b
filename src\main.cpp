/*
  ESP32-CAM Gemini with TTS Audio Output for Visually Impaired
  Manual photo capture on serial input with speech synthesis
  
  Features:
  - Manual photo capture triggered by any serial input
  - Sends image to Google Gemini 2.0 Flash API
  - Converts response to speech using BackgroundAudioSpeech library
  - Optimized prompts for navigation assistance for visually impaired users
  - Real-time image analysis with audio feedback
  
  Hardware Requirements:
  - ESP32-CAM module
  - PCM5102 I2S DAC
  - Speaker or headphones
  
  Wiring:
  ESP32-CAM -> PCM5102
  GPIO 41   -> BCK (Bit Clock)
  GPIO 42   -> LRC (Word Select)
  GPIO 2    -> DIN (Data Input)
  3.3V      -> VCC
  GND       -> GND
  GND       -> SCK (tie to ground - I2S slave mode)
  3.3V      -> FLT (tie to 3.3V - normal latency)
  GND       -> DMP (tie to ground - normal operation)
  3.3V      -> FMT (tie to 3.3V - I2S format)
*/

#include <WiFi.h>
#include <HTTPClient.h>
#include "esp_camera.h"
#include <ArduinoJson.h>
#include <BackgroundAudioSpeech.h>
#include <ESP32I2SAudio.h>

// Include English voice
#include <libespeak-ng/voice/en_us.h>

// WiFi credentials - CHANGE THESE
const char* ssid = "Home";
const char* password = "iopiopiop";

// Google Gemini API key - CHANGE THIS
const String apiKey = "AIzaSyCZoI9mSyZ8HhycR0MP_2wZQXG2PQUfy8o";

// Modified prompt for visually impaired navigation assistance
const String prompt = "You are an AI assistant helping a visually impaired person navigate their environment. "
                     "Describe what you see in this image in a clear, concise way focusing on: "
                     "1. Important objects and their locations using terms like left, right, center, close, far "
                     "2. Any obstacles, stairs, or hazards to avoid "
                     "3. People or moving objects nearby "
                     "4. Text, signs, or numbers that might be relevant "
                     "5. Overall scene context for safe navigation "
                     "Keep your response under 80 words and speak naturally as if helping someone navigate.";

// Audio setup - PCM5102 uses same I2S pins as CS4344
ESP32I2SAudio audio(41, 42, 2); // BCLK, LRCLK, DOUT pins (changed to unused pins)
BackgroundAudioSpeech speech(audio);

// =================================================================================
// -- CAMERA PIN DEFINITION (ESP32-S3-WROOM-1-N8R8) --
// =================================================================================
#define PWDN_GPIO_NUM   -1
#define RESET_GPIO_NUM  -1
#define XCLK_GPIO_NUM   15
#define SIOD_GPIO_NUM   4
#define SIOC_GPIO_NUM   5
#define Y9_GPIO_NUM     16
#define Y8_GPIO_NUM     17
#define Y7_GPIO_NUM     18
#define Y6_GPIO_NUM     12
#define Y5_GPIO_NUM     10
#define Y4_GPIO_NUM     8
#define Y3_GPIO_NUM     9
#define Y2_GPIO_NUM     11
#define VSYNC_GPIO_NUM  6
#define HREF_GPIO_NUM   7
#define PCLK_GPIO_NUM   13

// Base64 encoding character set
const char* base64_chars = 
    "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    "abcdefghijklmnopqrstuvwxyz"
    "0123456789+/";

// System state management
enum SystemState {
    SYSTEM_IDLE,
    SYSTEM_CAPTURING,
    SYSTEM_PROCESSING,
    SYSTEM_SPEAKING,
    SYSTEM_ERROR
};

SystemState currentState = SYSTEM_IDLE;

// Function prototypes
void captureAndAnalyzeImage();
void analyzeImage(const String& base64Image);
void speakText(const String& text);
void playStatusSound(const String& status);

// Function to encode data to base64
String encodeImageToBase64(uint8_t* data, size_t length) {
    String base64 = "";
    int i = 0;
    int j = 0;
    uint8_t char_array_3[3];
    uint8_t char_array_4[4];

    while (length--) {
        char_array_3[i++] = *(data++);
        if (i == 3) {
            char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
            char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
            char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
            char_array_4[3] = char_array_3[2] & 0x3f;

            for (i = 0; i < 4; i++)
                base64 += base64_chars[char_array_4[i]];
            i = 0;
        }
    }

    if (i) {
        for (j = i; j < 3; j++)
            char_array_3[j] = '\0';

        char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
        char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
        char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);

        for (j = 0; j < i + 1; j++)
            base64 += base64_chars[char_array_4[j]];

        while (i++ < 3)
            base64 += '=';
    }

    return base64;
}

void speakText(const String& text) {
    currentState = SYSTEM_SPEAKING;
    Serial.println("Speaking: " + text);
    
    // Convert String to char array and speak
    char* textBuffer = new char[text.length() + 1];
    text.toCharArray(textBuffer, text.length() + 1);
    
    speech.write(textBuffer, text.length() + 1);
    
    delete[] textBuffer;
    
    // Wait for speech to complete
    while (!speech.done()) {
        delay(10);
    }
    
    currentState = SYSTEM_IDLE;
}

void playStatusSound(const String& status) {
    if (status == "ready") {
        speakText("Navigation assistant ready.");
    } else if (status == "processing") {
        speakText("Processing image.");
    } else if (status == "error") {
        speakText("Error occurred. Please try again.");
    }
}

void setup() {
    Serial.begin(115200);
    Serial.println("ESP32-CAM Navigation Assistant with PCM5102 TTS Starting...");

    // Initialize WiFi
    WiFi.begin(ssid, password);
    Serial.print("Connecting to WiFi");
    while (WiFi.status() != WL_CONNECTED) {
        delay(1000);
        Serial.print(".");
    }
    Serial.println();
    Serial.println("WiFi Connected!");
    Serial.print("IP Address: ");
    Serial.println(WiFi.localIP());

    // Initialize camera
    camera_config_t config;
    config.ledc_channel = LEDC_CHANNEL_0;
    config.ledc_timer = LEDC_TIMER_0;
    config.pin_d0 = Y2_GPIO_NUM;
    config.pin_d1 = Y3_GPIO_NUM;
    config.pin_d2 = Y4_GPIO_NUM;
    config.pin_d3 = Y5_GPIO_NUM;
    config.pin_d4 = Y6_GPIO_NUM;
    config.pin_d5 = Y7_GPIO_NUM;
    config.pin_d6 = Y8_GPIO_NUM;
    config.pin_d7 = Y9_GPIO_NUM;
    config.pin_xclk = XCLK_GPIO_NUM;
    config.pin_pclk = PCLK_GPIO_NUM;
    config.pin_vsync = VSYNC_GPIO_NUM;
    config.pin_href = HREF_GPIO_NUM;
    config.pin_sscb_sda = SIOD_GPIO_NUM;
    config.pin_sscb_scl = SIOC_GPIO_NUM;
    config.pin_pwdn = PWDN_GPIO_NUM;
    config.pin_reset = RESET_GPIO_NUM;
    config.xclk_freq_hz = 20000000;
    config.pixel_format = PIXFORMAT_JPEG;
    config.frame_size = FRAMESIZE_QVGA;
    config.jpeg_quality = 10;
    config.fb_count = 1;

    if (esp_camera_init(&config) != ESP_OK) {
        Serial.println("Camera init failed");
        currentState = SYSTEM_ERROR;
        return;
    }

    // Initialize TTS
    Serial.println("Initializing Text-to-Speech...");
    speech.setVoice(voice_en_us);
    speech.setRate(175);    // Words per minute
    speech.setPitch(50);    // Pitch adjustment
    speech.setWordGap(1);   // Word gap in 10ms units
    speech.begin();

    Serial.println("Camera initialized successfully");
    Serial.println("TTS initialized successfully");
    Serial.println("System ready for navigation assistance");
    Serial.println("Send any character to capture and analyze image");
    Serial.println("========================================");
    
    // Announce system ready
    delay(2000); // Give TTS time to initialize
    playStatusSound("ready");
}

void captureAndAnalyzeImage() {
    if (currentState != SYSTEM_IDLE) {
        Serial.println("System busy - please wait...");
        return;
    }

    Serial.println("\n--- Starting Image Capture for Navigation ---");
    currentState = SYSTEM_CAPTURING;
    
    // Announce processing
    playStatusSound("processing");
    
    // Clear any buffered frames first
    camera_fb_t* fb = esp_camera_fb_get();
    if (fb) {
        esp_camera_fb_return(fb);
    }
    
    // Small delay to ensure fresh frame
    delay(100);
    
    // Capture the fresh image
    fb = esp_camera_fb_get();
    if (!fb) {
        Serial.println("ERROR: Camera capture failed");
        currentState = SYSTEM_ERROR;
        playStatusSound("error");
        currentState = SYSTEM_IDLE;
        return;
    }

    Serial.printf("Image captured - Size: %d bytes\n", fb->len);
    
    // Encode to base64
    String base64Image = encodeImageToBase64(fb->buf, fb->len);
    
    // Return the frame buffer
    esp_camera_fb_return(fb);

    if (base64Image.isEmpty()) {
        Serial.println("ERROR: Failed to encode image to base64");
        currentState = SYSTEM_ERROR;
        playStatusSound("error");
        currentState = SYSTEM_IDLE;
        return;
    }

    Serial.println("Image encoded to base64 successfully");
    
    // Send to Gemini for analysis
    analyzeImage(base64Image);
}

void analyzeImage(const String& base64Image) {
    Serial.println("Sending image to Gemini API for navigation analysis...");
    currentState = SYSTEM_PROCESSING;

    // Prepare JSON payload
    DynamicJsonDocument doc(8192);
    JsonArray contents = doc.createNestedArray("contents");
    JsonObject content = contents.createNestedObject();
    JsonArray parts = content.createNestedArray("parts");
    
    // Add text part (navigation-focused question)
    JsonObject textPart = parts.createNestedObject();
    textPart["text"] = prompt;
    
    // Add image part
    JsonObject imagePart = parts.createNestedObject();
    JsonObject inlineData = imagePart.createNestedObject("inlineData");
    inlineData["mimeType"] = "image/jpeg";
    inlineData["data"] = base64Image;
    
    // Add generation config
    JsonObject genConfig = doc.createNestedObject("generationConfig");
    genConfig["maxOutputTokens"] = 150; // Optimized for concise navigation info

    String jsonPayload;
    serializeJson(doc, jsonPayload);

    // Send HTTP request
    HTTPClient http;
    String apiUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=" + apiKey;
    http.begin(apiUrl);
    http.addHeader("Content-Type", "application/json");
    http.setTimeout(30000);

    int httpResponseCode = http.POST(jsonPayload);

    if (httpResponseCode > 0) {
        String response = http.getString();
        Serial.printf("HTTP Response Code: %d\n", httpResponseCode);
        
        // Parse response
        DynamicJsonDocument responseDoc(8192);
        DeserializationError error = deserializeJson(responseDoc, response);
        
        if (error) {
            Serial.print("JSON parsing failed: ");
            Serial.println(error.c_str());
            Serial.println("Raw response:");
            Serial.println(response);
            currentState = SYSTEM_ERROR;
            playStatusSound("error");
            currentState = SYSTEM_IDLE;
        } else {
            // Extract and display the result
            String responseContent = responseDoc["candidates"][0]["content"]["parts"][0]["text"].as<String>();
            
            Serial.println("\n========== NAVIGATION ANALYSIS ==========");
            Serial.println(responseContent);
            Serial.println("========================================\n");
            
            // Clean up the response text for better speech
            responseContent.replace("\"", "");
            responseContent.replace("*", "");
            responseContent.replace("#", "");
            responseContent.trim();
            
            // Speak the navigation information
            speakText(responseContent);
        }
    } else {
        Serial.printf("HTTP request failed with code: %d\n", httpResponseCode);
        Serial.println("Error: " + http.errorToString(httpResponseCode));
        currentState = SYSTEM_ERROR;
        playStatusSound("error");
        currentState = SYSTEM_IDLE;
    }

    http.end();
}

void loop() {
    // Handle serial input for manual trigger
    if (Serial.available() > 0) {
        // Clear the entire input buffer
        while (Serial.available()) {
            char c = Serial.read();
            
            // Handle voice commands via serial (for testing)
            if (c == 'h' || c == 'H') {
                speakText("Navigation assistant commands: Press any key to analyze current view. System is ready for navigation assistance.");
                continue;
            }
        }
        
        // Trigger image capture and analysis
        captureAndAnalyzeImage();
    }
    captureAndAnalyzeImage();
    // Small delay to prevent excessive CPU usage
    delay(4000);
}